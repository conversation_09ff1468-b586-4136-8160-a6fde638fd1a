{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS", "program": "${workspaceFolder}/src/main.ts", "runtimeExecutable": "${env:HOME}/.nvm/versions/node/v20.18.0/bin/node", "runtimeArgs": ["-r", "ts-node/register", "-r", "tsconfig-paths/register"], "autoAttachChildProcesses": true, "console": "integratedTerminal", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"]}]}