import React from 'react';

const BufferDisplay: React.FC<any> = (props) => {
  // 尝试从不同的props结构中获取值
  const value = props.record?.params?.[props.property?.path] ||
                props.record?.params?.[props.property?.name] ||
                props.value;

  const formatBufferOrValue = (val: any): string => {
    if (!val) return '-';

    // 处理 Buffer 对象
    if (val && typeof val === 'object' && val.type === 'Buffer') {
      // 如果 Buffer 数组为空，返回一个占位符
      if (!val.data || val.data.length === 0) {
        return '-';
      }
      // 尝试将 Buffer 转换为字符串
      try {
        return Buffer.from(val.data).toString();
      } catch (e) {
        return '-';
      }
    }

    // 处理其他类型
    try {
      return typeof val === 'object' ? JSON.stringify(val) : String(val);
    } catch (e) {
      return '-';
    }
  };

  return <span>{formatBufferOrValue(value)}</span>;
};

export default BufferDisplay;
