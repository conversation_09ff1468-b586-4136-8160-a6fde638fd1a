import React from 'react';

const DateDisplay: React.FC<any> = (props) => {
  // 尝试从不同的props结构中获取值
  const value = props.record?.params?.[props.property?.path] ||
                props.record?.params?.[props.property?.name] ||
                props.value;

  const formatDateValue = (val: any): string => {
    if (!val) return '-';

    // 检查是否是无效的日期字符串
    if (typeof val === 'string' && (val.includes('NaN') || val === '0NaN-aN-aN')) {
      return '-';
    }

    try {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        return '-';
      }
      return date.toLocaleDateString();
    } catch (e) {
      return '-';
    }
  };

  return <span>{formatDateValue(value)}</span>;
};

export default DateDisplay;
