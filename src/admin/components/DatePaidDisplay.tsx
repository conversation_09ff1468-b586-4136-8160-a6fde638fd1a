import React from 'react';

interface DatePaidDisplayProps {
  record?: any;
  property?: any;
}

const DatePaidDisplay: React.FC<DatePaidDisplayProps> = ({ record, property }) => {
  const value = record?.params?.[property?.path] || record?.params?.[property?.name];
  
  const formatDatePaid = (val: any): string => {
    if (!val) return '-';
    
    // 检查是否是无效的日期字符串
    if (typeof val === 'string' && (
      val.includes('NaN') || 
      val === '0NaN-aN-aN' || 
      val.includes('NaN:NaN') ||
      val === '0000-00-00' ||
      val === '0000-00-00 00:00:00'
    )) {
      return '-';
    }
    
    // 检查是否是空的日期对象或无效日期
    if (val instanceof Date) {
      if (isNaN(val.getTime()) || val.getTime() === 0) {
        return '-';
      }
    }
    
    try {
      const date = new Date(val);
      if (isNaN(date.getTime()) || date.getTime() === 0) {
        return '-';
      }
      return date.toLocaleDateString();
    } catch (e) {
      return '-';
    }
  };

  return <span>{formatDatePaid(value)}</span>;
};

export default DatePaidDisplay;
