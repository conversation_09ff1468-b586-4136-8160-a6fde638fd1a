import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text } from '@adminjs/design-system';

interface InvoiceItemsProps {
  record?: any;
  property?: any;
}

interface InvoiceItem {
  id: number;
  invoiceid: number;
  userid: number;
  relid: number;
  type: string;
  description: string;
  amount: number;
  duedate: string;
  paymentmethod: string;
  taxed: boolean;
  notes: string;
}

const InvoiceItems: React.FC<InvoiceItemsProps> = ({ record }) => {
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const invoiceId = record?.params?.id;

  useEffect(() => {
    const fetchInvoiceItems = async () => {
      if (!invoiceId) {
        setLoading(false);
        return;
      }

      try {
        const response = await api.resourceAction({
          resourceId: 'InvoiceItem',
          actionName: 'list',
          params: {
            filters: {
              invoiceid: invoiceId
            }
          }
        });
        
        if (response.data?.records) {
          setInvoiceItems(response.data.records.map((record: any) => record.params));
        }
      } catch (error) {
        console.error('Failed to fetch invoice items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInvoiceItems();
  }, [invoiceId]);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Hosting': return 'primary';
      case 'Upgrade': return 'success';
      case 'Flow': return 'info';
      case 'Balance': return 'warning';
      case 'Trial': return 'secondary';
      case 'Other': return 'default';
      default: return 'default';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '$0.00';
    return `$${Number(amount).toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString.includes('NaN') || dateString === '0000-00-00') return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  if (loading) {
    return <Text>Loading invoice items...</Text>;
  }

  if (invoiceItems.length === 0) {
    return (
      <Box p="lg" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px', textAlign: 'center' }}>
        <Text color="grey60">No invoice items found for this invoice.</Text>
      </Box>
    );
  }

  // Calculate total amount
  const totalAmount = invoiceItems.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);

  return (
    <Box>
      <Text variant="h4" mb="lg">Invoice Items ({invoiceItems.length})</Text>
      
      {/* Summary */}
      <Box 
        mb="lg" 
        p="lg" 
        style={{ 
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '8px',
          border: '1px solid #dee2e6'
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Text variant="h6" color="grey80">Items Summary</Text>
          <Text variant="h5" style={{ fontWeight: 700 }}>
            Total: {formatAmount(totalAmount)}
          </Text>
        </Box>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>Amount</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Service</TableCell>
            <TableCell>Taxed</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoiceItems.map((item) => (
            <TableRow key={item.id}>
              <TableCell>{item.id}</TableCell>
              <TableCell>
                <Badge variant={getTypeColor(item.type)}>
                  {item.type}
                </Badge>
              </TableCell>
              <TableCell>
                <Text style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                  {item.description || '-'}
                </Text>
              </TableCell>
              <TableCell>
                <Text style={{ fontWeight: 600 }}>
                  {formatAmount(item.amount)}
                </Text>
              </TableCell>
              <TableCell>{formatDate(item.duedate)}</TableCell>
              <TableCell>
                {item.relid ? (
                  <Link href={`/admin/resources/Service/records/${item.relid}/show`}>
                    Service #{item.relid}
                  </Link>
                ) : '-'}
              </TableCell>
              <TableCell>
                <Badge variant={item.taxed ? 'success' : 'secondary'}>
                  {item.taxed ? 'Yes' : 'No'}
                </Badge>
              </TableCell>
              <TableCell>
                <Link href={`/admin/resources/InvoiceItem/records/${item.id}/show`}>
                  View Details
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default InvoiceItems;
