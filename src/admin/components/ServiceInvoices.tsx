import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text } from '@adminjs/design-system';

interface ServiceInvoicesProps {
  record?: any;
  property?: any;
}

interface Invoice {
  id: number;
  invoicenum: string;
  userid: number;
  date: string;
  duedate: string;
  datepaid: string;
  status: string;
  total: number;
  paymentmethod: string;
  notes: string;
}

interface InvoiceItem {
  id: number;
  invoiceid: number;
  amount: number;
  type: string;
  description: string;
  duedate: string;
}

const ServiceInvoices: React.FC<ServiceInvoicesProps> = ({ record }) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const serviceId = record?.params?.id;

  useEffect(() => {
    const fetchServiceInvoices = async () => {
      if (!serviceId) {
        setLoading(false);
        return;
      }

      try {
        // 首先获取与此服务相关的发票项目
        const itemsResponse = await api.resourceAction({
          resourceId: 'InvoiceItem',
          actionName: 'list',
          params: {
            filters: {
              relid: serviceId
            }
          }
        });
        
        if (itemsResponse.data?.records) {
          const items = itemsResponse.data.records.map((record: any) => record.params);
          setInvoiceItems(items);
          
          // 获取唯一的发票ID
          const invoiceIds = [...new Set(items.map((item: InvoiceItem) => item.invoiceid))];
          
          // 获取这些发票的详细信息
          const invoicePromises = invoiceIds.map(async (invoiceId) => {
            const response = await api.recordAction({
              resourceId: 'Invoice',
              recordId: String(invoiceId),
              actionName: 'show',
            });
            return response.data?.record?.params;
          });

          const invoicesData = await Promise.all(invoicePromises);
          setInvoices(invoicesData.filter(Boolean) as Invoice[]);
        }
      } catch (error) {
        console.error('Failed to fetch service invoices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchServiceInvoices();
  }, [serviceId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'success';
      case 'Unpaid': return 'danger';
      case 'Cancelled': return 'secondary';
      case 'Refunded': return 'warning';
      case 'Collections': return 'danger';
      case 'Payment Pending': return 'warning';
      default: return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString.includes('NaN') || dateString === '0000-00-00') return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '$0.00';
    return `$${Number(amount).toFixed(2)}`;
  };

  const isOverdue = (duedate: string, status: string) => {
    if (status === 'Paid' || !duedate) return false;
    try {
      const due = new Date(duedate);
      const now = new Date();
      return due < now;
    } catch {
      return false;
    }
  };

  const getServiceItemsForInvoice = (invoiceId: number) => {
    return invoiceItems.filter(item => item.invoiceid === invoiceId);
  };

  if (loading) {
    return <Text>Loading service invoices...</Text>;
  }

  if (invoices.length === 0) {
    return (
      <Box p="lg" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px', textAlign: 'center' }}>
        <Text color="grey60">No invoices found for this service.</Text>
      </Box>
    );
  }

  // 计算统计信息
  const totalAmount = invoices.reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const paidAmount = invoices
    .filter(invoice => invoice.status === 'Paid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const unpaidAmount = invoices
    .filter(invoice => invoice.status === 'Unpaid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const overdueCount = invoices.filter(invoice => 
    isOverdue(invoice.duedate, invoice.status)
  ).length;

  return (
    <Box>
      <Text variant="h4" mb="lg">Service Related Invoices ({invoices.length})</Text>
      
      {/* 统计信息 */}
      <Box 
        mb="lg" 
        p="xl" 
        style={{ 
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '8px',
          border: '1px solid #dee2e6',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
        }}
      >
        <Text variant="h6" mb="lg" color="grey80" style={{ fontWeight: 600 }}>
          Invoice Summary
        </Text>
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg">
          <Box 
            p="md" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Total Amount
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {formatAmount(totalAmount)}
            </Text>
          </Box>
          <Box 
            p="md" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '6px',
              border: '1px solid #d4edda',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Paid Amount
            </Text>
            <Text variant="h5" mt="xs" color="success" style={{ fontWeight: 700 }}>
              {formatAmount(paidAmount)}
            </Text>
          </Box>
          <Box 
            p="md" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Unpaid Amount
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {formatAmount(unpaidAmount)}
            </Text>
          </Box>
          <Box 
            p="md" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Overdue Invoices
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {overdueCount}
            </Text>
          </Box>
        </Box>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Invoice #</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Total</TableCell>
            <TableCell>Service Items</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.map((invoice) => {
            const serviceItems = getServiceItemsForInvoice(invoice.id);
            const serviceItemsAmount = serviceItems.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
            
            return (
              <TableRow key={invoice.id}>
                <TableCell>{invoice.invoicenum || invoice.id}</TableCell>
                <TableCell>{formatDate(invoice.date)}</TableCell>
                <TableCell>
                  <Text color={isOverdue(invoice.duedate, invoice.status) ? 'danger' : 'default'}>
                    {formatDate(invoice.duedate)}
                  </Text>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusColor(invoice.status)}>
                    {invoice.status}
                  </Badge>
                </TableCell>
                <TableCell>{formatAmount(invoice.total)}</TableCell>
                <TableCell>
                  <Text variant="sm">
                    {serviceItems.length} items ({formatAmount(serviceItemsAmount)})
                  </Text>
                </TableCell>
                <TableCell>
                  <Link href={`/admin/resources/Invoice/records/${invoice.id}/show`}>
                    View Details
                  </Link>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </Box>
  );
};

export default ServiceInvoices;
