import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Link } from '@adminjs/design-system';

interface ServiceReferenceProps {
  record?: any;
  property?: any;
  resource?: any;
}

const ServiceReference: React.FC<ServiceReferenceProps> = ({ record, property }) => {
  const [serviceDomain, setServiceDomain] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const serviceId = record?.params?.[property?.path] || record?.params?.[property?.name];

  useEffect(() => {
    const fetchService = async () => {
      if (!serviceId) {
        setLoading(false);
        return;
      }

      try {
        const response = await api.recordAction({
          resourceId: 'Service',
          recordId: serviceId,
          actionName: 'show',
        });
        
        if (response.data?.record?.params?.domain) {
          setServiceDomain(response.data.record.params.domain);
        }
      } catch (error) {
        console.error('Failed to fetch service:', error);
        setServiceDomain(`Service ID: ${serviceId}`);
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [serviceId]);

  if (loading) {
    return <span>Loading...</span>;
  }

  if (!serviceId) {
    return <span>-</span>;
  }

  return (
    <Link href={`/admin/resources/Service/records/${serviceId}/show`}>
      {serviceDomain || `Service ID: ${serviceId}`}
    </Link>
  );
};

export default ServiceReference;
