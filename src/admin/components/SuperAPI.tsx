import React, { useEffect, useState } from 'react';
import { Box, Text, Loader } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import styled from 'styled-components';
import { SuperapiProductService } from '../resources/superapi-types.js';

const InfoCard = styled(Box)`
  background: #f5f7f9;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
`;

interface SuperAPIProps {
  serviceId: string;
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = bytes / Math.pow(k, i);
  
  return `${value.toFixed(2)} ${sizes[i]}`;
};

const SuperAPI: React.FC<SuperAPIProps> = ({ serviceId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiInfo, setApiInfo] = useState<SuperapiProductService | null>(null);
  const api = new ApiClient();

  useEffect(() => {
    const fetchSuperAPIInfo = async () => {
      try {
        const response = await api.resourceAction({
          resourceId: 'Service',
          actionName: 'superapi',
          params: {
            serviceId,
          },
        });
        
        setApiInfo(response.data as SuperapiProductService);
        setError(null);
      } catch (err) {
        setError('Failed to fetch SuperAPI information');
        console.error('SuperAPI fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    if (serviceId) {
      fetchSuperAPIInfo();
    }
  }, [serviceId]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  if (!apiInfo) {
    return (
      <Box>
        <Text>No SuperAPI information available</Text>
      </Box>
    );
  }

  return (
    <InfoCard>
      <Box>
        <Text variant="h5">Service Details</Text>
        <Box mt="lg">
          <Text>ID: {apiInfo.id}</Text>
          <Text>Client ID: {apiInfo.clientId}</Text>
          <Text>Client: {apiInfo.client}</Text>
          <Text>Username: {apiInfo.username}</Text>
          <Text>Password: {apiInfo.password}</Text>
          <Text>Status: {apiInfo.status}</Text>
          <Text>Quota: {apiInfo.quota}</Text>
          <Text>Type: {apiInfo.type}</Text>
          <Text>Upload: {formatBytes(apiInfo.upload)}</Text>
          <Text>Download: {formatBytes(apiInfo.download)}</Text>
          <Text>Reset Date: {apiInfo.resetDate}</Text>
          <Text>Updated At: {apiInfo.updatedAt}</Text>
          <Text>Expired At: {apiInfo.expiredAt}</Text>
          <Text>Speed Limit: {apiInfo.speedLimit}</Text>
        </Box>
      </Box>
    </InfoCard>
  );
};

export default SuperAPI; 