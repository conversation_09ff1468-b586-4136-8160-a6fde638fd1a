import React, { useState } from 'react';
import { Box, Button, Text, Input, MessageBox } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import styled from 'styled-components';

const PasswordContainer = styled(Box)`
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

interface UpdatePasswordProps {
  userId: string;
  onSuccess?: () => void;
}

const UpdatePassword: React.FC<UpdatePasswordProps> = ({ userId, onSuccess }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const api = new ApiClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset states
    setError(null);
    setSuccess(false);

    // Validate passwords
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    try {
      await api.resourceAction({
        resourceId: 'User',
        actionName: 'update-password',
        method: 'post',
        data: {
          userId,
          password,
        },
      });

      setSuccess(true);
      setPassword('');
      setConfirmPassword('');
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setError(err.message || 'Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <PasswordContainer>
      <Text variant="lg" fontWeight="bold" marginBottom="lg">
        Update Password
      </Text>

      <form onSubmit={handleSubmit}>
        <Box marginBottom="lg">
          <Text marginBottom="sm">New Password</Text>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter new password"
            width={1}
          />
        </Box>

        <Box marginBottom="lg">
          <Text marginBottom="sm">Confirm Password</Text>
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm new password"
            width={1}
          />
        </Box>

        {error && (
          <MessageBox
            message={error}
            variant="danger"
            marginBottom="lg"
          />
        )}

        {success && (
          <MessageBox
            message="Password updated successfully"
            variant="success"
            marginBottom="lg"
          />
        )}

        <Button
          variant="primary"
          type="submit"
          disabled={loading}
        >
          {loading ? 'Updating...' : 'Update Password'}
        </Button>
      </form>
    </PasswordContainer>
  );
};

export default UpdatePassword; 