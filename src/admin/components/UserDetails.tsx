import React, { useState } from 'react';
import { Box, Button, Text } from '@adminjs/design-system';
import UserServices from './UserServices.js';
import UserInvoices from './UserInvoices.js';

interface UserDetailsProps {
  record?: any;
  property?: any;
}

const UserDetails: React.FC<UserDetailsProps> = ({ record, property }) => {
  const [activeTab, setActiveTab] = useState<'services' | 'invoices'>('services');

  if (!record?.params?.id) {
    return <Text>No user data available.</Text>;
  }

  return (
    <Box>
      <Text variant="h3" mb="lg">User Related Data</Text>
      
      {/* Tab Navigation */}
      <Box mb="lg" display="flex" gap="sm">
        <Button
          variant={activeTab === 'services' ? 'primary' : 'text'}
          onClick={() => setActiveTab('services')}
        >
          Services
        </Button>
        <Button
          variant={activeTab === 'invoices' ? 'primary' : 'text'}
          onClick={() => setActiveTab('invoices')}
        >
          Invoices
        </Button>
      </Box>

      {/* Tab Content */}
      <Box>
        {activeTab === 'services' && (
          <UserServices record={record} property={property} />
        )}
        {activeTab === 'invoices' && (
          <UserInvoices record={record} property={property} />
        )}
      </Box>
      <br />
    </Box>
  );
};

export default UserDetails;
