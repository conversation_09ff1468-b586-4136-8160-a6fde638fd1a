import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text } from '@adminjs/design-system';

interface UserInvoicesProps {
  record?: any;
  property?: any;
}

interface Invoice {
  id: number;
  invoicenum: string;
  date: string;
  duedate: string;
  datepaid: string;
  status: string;
  total: number;
  paymentmethod: string;
}

const UserInvoices: React.FC<UserInvoicesProps> = ({ record }) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const userId = record?.params?.id;

  useEffect(() => {
    const fetchUserInvoices = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        // 获取用户的所有账单
        const response = await api.resourceAction({
          resourceId: 'Invoice',
          actionName: 'list',
          params: {
            filters: {
              userid: userId
            }
          }
        });
        
        if (response.data?.records) {
          setInvoices(response.data.records.map((record: any) => record.params));
        }
      } catch (error) {
        console.error('Failed to fetch user invoices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInvoices();
  }, [userId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'success';
      case 'Unpaid': return 'danger';
      case 'Cancelled': return 'secondary';
      case 'Refunded': return 'warning';
      case 'Collections': return 'danger';
      case 'Payment Pending': return 'warning';
      default: return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString.includes('NaN') || dateString === '0000-00-00') return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '-';
    return `$${Number(amount).toFixed(2)}`;
  };

  const isOverdue = (duedate: string, status: string) => {
    if (status === 'Paid' || !duedate) return false;
    try {
      const due = new Date(duedate);
      const now = new Date();
      return due < now;
    } catch {
      return false;
    }
  };

  if (loading) {
    return <Text>Loading invoices...</Text>;
  }

  if (invoices.length === 0) {
    return <Text>No invoices found for this user.</Text>;
  }

  // 计算统计信息
  const totalAmount = invoices.reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const paidAmount = invoices
    .filter(invoice => invoice.status === 'Paid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const unpaidAmount = invoices
    .filter(invoice => invoice.status === 'Unpaid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const overdueCount = invoices.filter(invoice => 
    isOverdue(invoice.duedate, invoice.status)
  ).length;

  return (
    <Box>
      <Text variant="h4" mb="lg">User Invoices ({invoices.length})</Text>
      
      {/* 统计信息 */}
      <Box mb="lg" p="lg" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <Box display="flex" flexWrap="wrap" gap="lg">
          <Box>
            <Text variant="sm" color="grey60">Total Amount</Text>
            <Text variant="h6">{formatAmount(totalAmount)}</Text>
          </Box>
          <Box>
            <Text variant="sm" color="grey60">Paid Amount</Text>
            <Text variant="h6" color="success">{formatAmount(paidAmount)}</Text>
          </Box>
          <Box>
            <Text variant="sm" color="grey60">Unpaid Amount</Text>
            <Text variant="h6" color="danger">{formatAmount(unpaidAmount)}</Text>
          </Box>
          <Box>
            <Text variant="sm" color="grey60">Overdue Invoices</Text>
            <Text variant="h6" color="danger">{overdueCount}</Text>
          </Box>
        </Box>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Invoice #</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Date Paid</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Total</TableCell>
            <TableCell>Payment Method</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.map((invoice) => (
            <TableRow key={invoice.id}>
              <TableCell>{invoice.invoicenum || invoice.id}</TableCell>
              <TableCell>{formatDate(invoice.date)}</TableCell>
              <TableCell>
                <Text color={isOverdue(invoice.duedate, invoice.status) ? 'danger' : 'default'}>
                  {formatDate(invoice.duedate)}
                </Text>
              </TableCell>
              <TableCell>{formatDate(invoice.datepaid)}</TableCell>
              <TableCell>
                <Badge variant={getStatusColor(invoice.status)}>
                  {invoice.status}
                </Badge>
              </TableCell>
              <TableCell>{formatAmount(invoice.total)}</TableCell>
              <TableCell>{invoice.paymentmethod || '-'}</TableCell>
              <TableCell>
                <Link href={`/admin/resources/Invoice/records/${invoice.id}/show`}>
                  View Details
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default UserInvoices;
