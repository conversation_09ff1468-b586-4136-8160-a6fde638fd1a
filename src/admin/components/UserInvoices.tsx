import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text } from '@adminjs/design-system';

interface UserInvoicesProps {
  record?: any;
  property?: any;
}

interface Invoice {
  id: number;
  invoicenum: string;
  date: string;
  duedate: string;
  datepaid: string;
  status: string;
  total: number;
  paymentmethod: string;
}

const UserInvoices: React.FC<UserInvoicesProps> = ({ record }) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const userId = record?.params?.id;

  useEffect(() => {
    const fetchUserInvoices = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        // 获取用户的所有账单
        const response = await api.resourceAction({
          resourceId: 'Invoice',
          actionName: 'list',
          params: {
            filters: {
              userid: userId
            }
          }
        });
        
        if (response.data?.records) {
          setInvoices(response.data.records.map((record: any) => record.params));
        }
      } catch (error) {
        console.error('Failed to fetch user invoices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserInvoices();
  }, [userId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'success';
      case 'Unpaid': return 'danger';
      case 'Cancelled': return 'secondary';
      case 'Refunded': return 'warning';
      case 'Collections': return 'danger';
      case 'Payment Pending': return 'warning';
      default: return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString.includes('NaN') || dateString === '0000-00-00') return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '-';
    return `$${Number(amount).toFixed(2)}`;
  };

  const isOverdue = (duedate: string, status: string) => {
    if (status === 'Paid' || !duedate) return false;
    try {
      const due = new Date(duedate);
      const now = new Date();
      return due < now;
    } catch {
      return false;
    }
  };

  if (loading) {
    return <Text>Loading invoices...</Text>;
  }

  if (invoices.length === 0) {
    return <Text>No invoices found for this user.</Text>;
  }

  // 计算统计信息
  const totalAmount = invoices.reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const paidAmount = invoices
    .filter(invoice => invoice.status === 'Paid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const unpaidAmount = invoices
    .filter(invoice => invoice.status === 'Unpaid')
    .reduce((sum, invoice) => sum + (Number(invoice.total) || 0), 0);
  const overdueCount = invoices.filter(invoice => 
    isOverdue(invoice.duedate, invoice.status)
  ).length;

  return (
    <Box>
      <Text variant="h4" mb="lg">User Invoices ({invoices.length})</Text>
      
      {/* 统计信息 */}
      <Box
        mb="lg"
        p="xl"
        style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '8px',
          border: '1px solid #dee2e6',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
        }}
      >
        <Text variant="h6" mb="lg" color="grey80" style={{ fontWeight: 600 }}>
          Invoice Summary
        </Text>
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg">
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Total Amount
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {formatAmount(totalAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #d4edda',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Paid Amount
            </Text>
            <Text variant="h5" mt="xs" color="success" style={{ fontWeight: 700 }}>
              {formatAmount(paidAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Unpaid Amount
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {formatAmount(unpaidAmount)}
            </Text>
          </Box>
          <Box
            p="md"
            style={{
              backgroundColor: 'white',
              borderRadius: '6px',
              border: '1px solid #f8d7da',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Overdue Invoices
            </Text>
            <Text variant="h5" mt="xs" color="danger" style={{ fontWeight: 700 }}>
              {overdueCount}
            </Text>
          </Box>
        </Box>
      </Box>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Invoice #</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Date Paid</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Total</TableCell>
            <TableCell>Payment Method</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.map((invoice) => (
            <TableRow key={invoice.id}>
              <TableCell>{invoice.invoicenum || invoice.id}</TableCell>
              <TableCell>{formatDate(invoice.date)}</TableCell>
              <TableCell>
                <Text color={isOverdue(invoice.duedate, invoice.status) ? 'danger' : 'default'}>
                  {formatDate(invoice.duedate)}
                </Text>
              </TableCell>
              <TableCell>{formatDate(invoice.datepaid)}</TableCell>
              <TableCell>
                <Badge variant={getStatusColor(invoice.status)}>
                  {invoice.status}
                </Badge>
              </TableCell>
              <TableCell>{formatAmount(invoice.total)}</TableCell>
              <TableCell>{invoice.paymentmethod || '-'}</TableCell>
              <TableCell>
                <Link href={`/admin/resources/Invoice/records/${invoice.id}/show`}>
                  View Details
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default UserInvoices;
