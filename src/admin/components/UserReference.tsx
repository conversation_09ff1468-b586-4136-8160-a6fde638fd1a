import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Link } from '@adminjs/design-system';

interface UserReferenceProps {
  record?: any;
  property?: any;
  resource?: any;
}

const UserReference: React.FC<UserReferenceProps> = ({ record, property }) => {
  const [userEmail, setUserEmail] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();

  // 尝试多种方式获取 userId
  const userId = record?.params?.[property?.path] ||
                 record?.params?.[property?.name] ||
                 record?.params?.userid ||
                 record?.params?.userId;

  useEffect(() => {
    const fetchUser = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        const response = await api.recordAction({
          resourceId: 'User',
          recordId: userId,
          actionName: 'show',
        });
        
        if (response.data?.record?.params?.email) {
          setUserEmail(response.data.record.params.email);
        }
      } catch (error) {
        console.error('Failed to fetch user:', error);
        setUserEmail('');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return <span>Loading...</span>;
  }

  if (!userId) {
    return <span>-</span>;
  }

  return (
    <Link href={`/admin/resources/User/records/${userId}/show`}>
      {userEmail || userId}
    </Link>
  );
};

export default UserReference;
