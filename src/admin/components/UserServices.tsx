import React, { useEffect, useState } from 'react';
import { ApiClient } from 'adminjs';
import { Box, Table, TableHead, TableBody, TableRow, TableCell, Link, Badge, Text } from '@adminjs/design-system';

interface UserServicesProps {
  record?: any;
  property?: any;
}

interface Service {
  id: number;
  domain: string;
  domainstatus: string;
  packageid: number;
  billingcycle: string;
  nextduedate: string;
  amount: number;
}

const UserServices: React.FC<UserServicesProps> = ({ record }) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const api = new ApiClient();
  
  const userId = record?.params?.id;

  useEffect(() => {
    const fetchUserServices = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        // 获取用户的所有服务
        const response = await api.resourceAction({
          resourceId: 'Service',
          actionName: 'list',
          params: {
            filters: {
              userid: userId
            }
          }
        });
        
        if (response.data?.records) {
          setServices(response.data.records.map((record: any) => record.params));
        }
      } catch (error) {
        console.error('Failed to fetch user services:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserServices();
  }, [userId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Suspended': return 'danger';
      case 'Pending': return 'warning';
      case 'Terminated': return 'danger';
      case 'Cancelled': return 'secondary';
      default: return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return '-';
    }
  };

  const formatAmount = (amount: number) => {
    if (!amount) return '-';
    return `$${Number(amount).toFixed(2)}`;
  };

  if (loading) {
    return <Text>Loading services...</Text>;
  }

  if (services.length === 0) {
    return <Text>No services found for this user.</Text>;
  }

  return (
    <Box>
      <Text variant="h4" mb="lg">User Services ({services.length})</Text>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Domain</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Billing Cycle</TableCell>
            <TableCell>Next Due Date</TableCell>
            <TableCell>Amount</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {services.map((service) => (
            <TableRow key={service.id}>
              <TableCell>{service.id}</TableCell>
              <TableCell>{service.domain || '-'}</TableCell>
              <TableCell>
                <Badge variant={getStatusColor(service.domainstatus)}>
                  {service.domainstatus}
                </Badge>
              </TableCell>
              <TableCell>{service.billingcycle || '-'}</TableCell>
              <TableCell>{formatDate(service.nextduedate)}</TableCell>
              <TableCell>{formatAmount(service.amount)}</TableCell>
              <TableCell>
                <Link href={`/admin/resources/Service/records/${service.id}/show`}>
                  View Details
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default UserServices;
