import { Entity, Column, PrimaryGeneratedColumn, BaseEntity, CreateDateColumn } from 'typeorm';

export enum EventsType {
    DownloadApps = 'DownloadApps',
    BindTg = 'BindTg',
    EnableFcm = 'EnableFcm',
    Questionnaire = 'Questionnaire',
    Claim = 'Claim',
    Trial = 'Trial'
}

@Entity('events')
export class Event extends BaseEntity  {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'int', width: 11, nullable: false, default: '0'})
    userId: number;

    @Column({type: 'varchar', nullable: false})
    type: EventsType;

    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @CreateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Column({
        type: 'json',
        nullable: true,
        transformer: {
            to: (value: object) => value,
            from: (value: object) => JSON.stringify(value, null, 2)
        }
    })
    context: string;

    toJson() {
        return {
            id: this.id,
            userId: this.userId,
            type: this.type,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            context: this.context,
        };
    }
}
