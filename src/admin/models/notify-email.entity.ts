import { BaseEntity, <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('notify_emails')
export class NotifyEmail extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ type: 'longtext', nullable: true })
  subject: string;

  @Column({ type: 'longtext', nullable: true })
  type: string;

  @Column({ type: 'longtext', nullable: true })
  content: string;

  @Column({ type: 'bigint', nullable: true, name: 'template_id' })
  templateId: number;

  @Column({ type: 'longtext', nullable: true, name: 'template_param' })
  templateParam: string;

  @Column({ type: 'bigint', nullable: true })
  sender: number;

  @Column({ type: 'bigint', nullable: true, name: 'filter_id' })
  filterId: number;

  @Column({ type: 'longtext', nullable: true, name: 'receiver_type' })
  receiverType: string;

  @Column({ type: 'longtext', nullable: true })
  receivers: string;

  @CreateDateColumn({ type: 'datetime', precision: 3, name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime', precision: 3, name: 'updated_at' })
  updatedAt: Date;
} 