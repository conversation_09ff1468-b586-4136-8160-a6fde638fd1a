import {
  BaseEntity,
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne,
  PrimaryGeneratedColumn} from "typeorm";
import { Product } from "./product.entity.js";

@Entity('tblpricing')
export class Pricing extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'varchar', nullable: false})
  type: string;

  @Column({type: 'int', nullable: false})
  relid: number;

  @Column({type: 'varchar', nullable: false})
  monthly: string;

  @Column({type: 'varchar', nullable: false})
  quarterly: string;

  @Column({type: 'varchar', nullable: false})
  semiannually: string;

  @Column({type: 'varchar', nullable: false})
  annually: string;

  @Column({type: 'varchar', nullable: false})
  biennially: string;

  @Column({type: 'varchar', nullable: false})
  triennially: string;

  @OneToOne(() => Product)
  @JoinColumn({name: 'relid'})
  product: Product;
}