import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tblproducts')
export class Product extends BaseEntity {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'varchar', nullable: false})
    name: string;

    @Column({type: 'int'})
    gid: number;

    @Column({type: 'varchar', nullable: false})
    description: string;

    @Column({type: 'tinyint'})
    hidden: number;
}