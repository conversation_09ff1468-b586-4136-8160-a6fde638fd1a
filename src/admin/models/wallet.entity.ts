import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, BaseEntity, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum WalletStatus {
  CREATED = 'CREATED', // default
}

@Entity('wallets')
export class Wallet extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'int', width: 11, nullable: false, default: '0', unique: true })
  userId: number;

  @Column({type: 'varchar', default: WalletStatus.CREATED})
  status: WalletStatus;

  @Column({type: 'float', width: 11, nullable: false, default: 0 })
  balance: number;

  @Column({ type: 'varchar', width: 20, nullable: false, default: '0', unique: true})
  address: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
  /**
   * get a new wallet instance from raw query object
   * @param raw
   */
  static fromRaw(raw): Wallet {
    const wallet = new Wallet();
    wallet.id = raw.id;
    wallet.userId = raw.userId;
    wallet.status = raw.status;
    wallet.balance = raw.balance;
    wallet.address = raw.address;
    wallet.createdAt = raw.createdAt;
    return wallet;
  }
}