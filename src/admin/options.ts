import { AdminJSOptions } from 'adminjs';

import { componentLoader, Components } from './component-loader.js';
import { User } from './models/user.entity.js';
import { Product } from './models/product.entity.js';
import { Service} from './models/service.entity.js';
import { Pricing } from './models/pricing.entity.js';
import { Invoice } from './models/invoice.entity.js';
import { InvoiceItem } from './models/invoice.entity.js';
import { Wallet } from './models/wallet.entity.js';
import { Event } from './models/events.entity.js';
import { ServiceResource } from './resources/service-resource.js';
import { UserResource } from './resources/user-resource.js';
import { InvoiceResource } from './resources/invoice-resource.js';
import { InvoiceItemResource } from './resources/invoice-item-resource.js';
import { NotifyEmail } from './models/notify-email.entity.js';

const options: AdminJSOptions = {
  componentLoader,
  dashboard: { component: Components.Dashboard },
  rootPath: '/admin',
  resources: [
    UserResource,
    Product,
    Pricing,
    ServiceResource,
    InvoiceResource,
    InvoiceItemResource,
    Wallet,
    Event,
    NotifyEmail
  ],
  databases: [],
  locale: {
    language: 'en',
    translations: {
      labels: {
        User: 'User',
        Product: 'Product',
        Pricing: 'Pricing',
        Service: 'Service',
        Invoice: 'Invoice',
        InvoiceItem: 'Invoice Item',
        Wallet: 'Wallet',
        Event: 'Event',
        NotifyEmail: 'Notify Email',
        whmcs: 'WHMCS',
        api: 'API',
        notify: 'Notify'
      },
      resources: {
        User: {
          properties: {
            id: 'ID',
            email: 'Email',
            firstname: 'First Name',
            lastname: 'Last Name',
            emailVerified: 'Email Verified',
            status: 'Status',
            datecreated: 'Date Created',
            createdAt: 'Created At',
            updatedAt: 'Updated At',
            cardLastFour: 'Card Last Four',
            gatewayId: 'Gateway ID',
            lastLogin: 'Last Login',
            ip: 'IP Address',
            host: 'Host',
            pwResetKey: 'Password Reset Key',
            pwResetExpiry: 'Password Reset Expiry',
            emailOptOut: 'Email Opt Out',
            overrideAutoClose: 'Override Auto Close',
            allowSso: 'Allow SSO',
            emailPreferences: 'Email Preferences',
            billingCid: 'Billing CID'
          }
        }
      }
    }
  }
};

export default options;
