import { Invoice } from "../models/invoice.entity.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

export const InvoiceResource: ResourceWithOptions = {
  resource: Invoice,
  options: {
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'userid',
      'date',
      'duedate',
      'status',
      'total',
      'paymentmethod',
      'datepaid',
      'createdAt'
    ],
    // 自定义详情页面字段显示顺序
    showProperties: [
      'id',
      'userid',
      'status',
      'date',
      'duedate',
      'datepaid',
      'paymentmethod',
      'total',
      'subtotal',
      'tax',
      'tax2',
      'taxrate',
      'taxrate2',
      'credit',
      'notes',
      'createdAt',
      'updatedAt',
      'invoiceItems'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number',
        isTitle: true
      },
      userid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User',
        components: {
          list: Components.UserReference,
          show: Components.UserReference
        }
      },
      invoicenum: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
      },
      date: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date'
      },
      duedate: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date'
      },
      datepaid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date',
        components: {
          list: Components.DatePaidDisplay,
          show: Components.DatePaidDisplay
        }
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        availableValues: [
          { value: 'Unpaid', label: 'Unpaid' },
          { value: 'Paid', label: 'Paid' },
          { value: 'Cancelled', label: 'Cancelled' },
          { value: 'Refunded', label: 'Refunded' },
          { value: 'Collections', label: 'Collections' },
          { value: 'Payment Pending', label: 'Payment Pending' }
        ]
      },
      paymentmethod: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      total: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      subtotal: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      tax: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      tax2: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      taxrate: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      taxrate2: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      credit: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      },
      updatedAt: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime'
      },
      invoiceItems: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.InvoiceItems
        }
      }
    }
  }
};
