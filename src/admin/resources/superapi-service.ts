import { ReqCreateSuperapiService, ReqFindSuperapiService, SuperapiProductService } from "./superapi-types.js";

export class SuperapiService {
    private readonly productName: string;
    private readonly apiHost: string;
  
    constructor(config: { PRODUCT_NAME: string; SUPERAPI_HOST: string }) {
      this.productName = config.PRODUCT_NAME;
      this.apiHost = config.SUPERAPI_HOST;
    }
  
    private async handleRequest<T>(
      url: string,
      options: RequestInit,
      errorConfig: { error: string; message: string }
    ): Promise<T> {
      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });
  
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
  
        const contentType = response.headers.get('content-type');
        if (contentType?.includes('application/json')) {
          return await response.json();
        }
        return await response.text() as T;
      } catch (e) {
        console.error(`${errorConfig.error}: ${(e as Error).message}`);
        throw new Error(errorConfig.message);
      }
    }
  
    async fetchService(body: ReqFindSuperapiService): Promise<SuperapiProductService> {
      return this.handleRequest<SuperapiProductService>(
        `${this.apiHost}/api/v1/services/show`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: body.serviceId,
            client: this.productName,
          }),
        },
        {
          error: 'FetchServiceError',
          message: 'Network error, can not fetch service',
        }
      );
    }
  
    async createService(body: ReqCreateSuperapiService): Promise<boolean> {
      const result = await this.handleRequest<SuperapiProductService>(
        `${this.apiHost}/api/v1/services`,
        {
          method: 'POST',
          body: JSON.stringify(body),
        },
        {
          error: 'CreateServiceError',
          message: 'Network error, can not create service',
        }
      );
      return result.clientId === body.clientId;
    }
  
    async fetchSubscription(client: string, id: number, binary?: string, isSelfDevClient?: string): Promise<string> {
      const params = new URLSearchParams({
        agent: client,
        ...(binary && { binary: 'yes' }),
        ...(isSelfDevClient && { isSelfDevClient: 'yes' }),
      });
  
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/${this.productName}/${id}/subscription?${params}`,
        { method: 'GET' },
        {
          error: 'FetchSubscriptionError',
          message: 'Network error, can not fetch subscription',
        }
      );
    }
  
    packageId2packageInfo(packageId: number) {
      if (packageId === 33 || packageId === 2) {
        return JSON.stringify([{traffic: 107374182400, speed: 2621440}]);
      }
      if (packageId === 34 || packageId === 3) {
        return JSON.stringify([{traffic: 214748364800, speed: 3932160}]);
      }
      return JSON.stringify([{traffic: 53687091200, speed: 1310720}]);
    }
  
    async syncServicePackage(serviceId: number, packageId: number): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/update`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: serviceId,
            client: this.productName,
            quota: this.packageId2packageInfo(packageId),
          }),
        },
        {
          error: 'SyncPackageError',
          message: 'Network error, can not sync package',
        }
      );
    }
  
    async getClusterInfos(): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/infos`,
        { method: 'GET' },
        {
          error: 'FetchNodeInfosError',
          message: 'Network error, can not fetch node infos',
        }
      );
    }
  
    async syncServiceExpireDate(serviceId: number, nextDueDate: Date): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/update`,
        {
          method: 'POST',
          body: JSON.stringify({
            clientId: serviceId,
            client: this.productName,
            expiredAt: nextDueDate,
          }),
        },
        {
          error: 'SyncExpireDateError',
          message: 'Network error, can not sync expire date',
        }
      );
    }
  
    async redeem(serviceId: number | string, delta: number | string): Promise<any> {
      return this.handleRequest(
        `${this.apiHost}/api/v1/services/${this.productName}/${serviceId}/reduceUsage`,
        {
          method: 'POST',
          body: JSON.stringify({ delta }),
        },
        {
          error: 'RedeemDataError',
          message: 'Network error, can not redeem data',
        }
      );
    }
  
    async reset(serviceId: number | string): Promise<string> {
      return this.handleRequest<string>(
        `${this.apiHost}/api/v1/services/reset`,
        {
          method: 'POST',
          body: JSON.stringify({
            client: this.productName,
            clientId: serviceId,
          }),
        },
        {
          error: 'ResetServiceError',
          message: 'Network error, can not reset this service',
        }
      );
    }
  }