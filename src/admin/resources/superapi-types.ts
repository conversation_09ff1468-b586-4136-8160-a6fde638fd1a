import * as crypto from 'crypto';
export interface SuperapiProductService {
  id: number;
  clientId: number;
  client: number;
  username: string;
  password: string;
  status: Date;
  quota: string;
  type: ServiceResetType;
  upload: number;
  download: number;
  resetDate: Date;
  updatedAt: Date;
  expiredAt: Date;
  speedLimit: number;

}

export enum SuperapiServiceStatus {
  Suspended = 0,
  Active = 1,
}

export enum ServiceResetType {
  Never = 0,
  Monthly = 1,
}

export interface ReqFindSuperapiService {
  serviceId: number;
}

export interface ReqCreateSuperapiService {
  client: string;
  clientId: number;
  quota: string;
  status: SuperapiServiceStatus;
  expiredAt: string;
  resetType: number;
}

export class Service {
  public id: string;
  public name: string;
  public nextduedate: string;
  public status: string;
  public subscribeUrl: string;
  public regdate: string;
  public billingcycle: string;
  public productId: string;

  static fromRaw(p): Service {
    const service = new Service();
    service.id = p.id;
    service.name = p.name;
    service.nextduedate = p.nextduedate;
    service.regdate = p.regdate;
    service.status = p.status;
    service.billingcycle = p.billingcycle.toLowerCase();
    if (service.billingcycle === 'semi-annually') {
      service.billingcycle = 'semiannually';
    }
    service.productId = p.pid;
    service.subscribeUrl = `/api/v1/services/${service.id}/${Service.token(p.id)}`;
    return service;
  }
  static token(id): string {
    return crypto.createHash('md5').update(`${id}-jss-is-fucking-awesome`).digest('hex');
  }
}
