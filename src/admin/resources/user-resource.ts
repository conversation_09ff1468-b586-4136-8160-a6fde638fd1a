import { User } from "../models/user.entity.js";
import bcrypt from 'bcrypt';
import { Components } from '../component-loader.js';

export const UserResource = {
  resource: User,
  options: {
    showProperties: [
      'id',
      'email',
      'emailVerified',
      'status',
      'datecreated',
      'lastLogin',
      'createdAt',
      'updatedAt',
      'userRelatedData',
      // 基本信息
      'firstname',
      'lastname',
      'phoneNumber',
      'companyName',
      // 地址信息
      'address1',
      'address2',
      'city',
      'state',
      'postCode',
      'country',
      // 账户设置
      'language',
      'currency',
      'emailPreferences',
      'emailOptOut',
      'marketingOptIn',
      // 支付信息
      'cardType',
      'cardLastFour',
      'expDate',
      'startDate',
      'bankName',
      'gatewayId',
      'defaultGateway',
      // 账户管理
      'credit',
      'taxExempt',
      'taxId',
      'billingCid',
      'groupId',
      // 安全设置
      'allowSso',
      'authModule',
      'ip',
      'host',
      'uuid',
      // 账户控制
      'overrideAutoClose',
      'lateFeeOveride',
      'overideDueNotices',
      'separateInvoices',
      'disableAutoCC',
      // 密码重置
      'pwResetKey',
      'pwResetExpiry',
      // 备注
      'notes'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      email: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        isTitle: true
      },
      firstname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      lastname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      emailVerified: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        availableValues: [
          { value: 'Active', label: 'Active' },
          { value: 'Inactive', label: 'Inactive' },
          { value: 'Closed', label: 'Closed' }
        ]
      },
      datecreated: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'date'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.DateDisplay,
          list: Components.DateDisplay
        }
      },
      updatedAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'date'
      },
      password: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'string'
      },
      cardLastFour: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      gatewayId: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      lastLogin: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      ip: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      host: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      pwResetKey: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string'
      },
      pwResetExpiry: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'date'
      },
      emailOptOut: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      overrideAutoClose: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      allowSso: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      emailPreferences: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      billingCid: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      uuid: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      language: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      country: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      marketingOptIn: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      companyName: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      address1: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      address2: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      city: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      state: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      postCode: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      phoneNumber: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      taxId: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      authModule: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string'
      },
      authData: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'string'
      },
      currency: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      defaultGateway: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      credit: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      taxExempt: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      lateFeeOveride: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      overideDueNotices: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      separateInvoices: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      disableAutoCC: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      securityQid: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'number'
      },
      securityQans: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'string'
      },
      groupId: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      cardType: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      bankName: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string'
      },
      expDate: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      startDate: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      cardNum: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      issueNumber: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      bankCode: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      bankAcct: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      bankType: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.BufferDisplay,
          list: Components.BufferDisplay
        }
      },
      userRelatedData: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.UserDetails
        }
      }
    },
    actions: {
      'update-password': {
        actionType: 'record',
        handler: async (request, response, context) => {
          const { userId, password } = request.payload;
          
          if (!password || password.length < 8) {
            throw new Error('Password must be at least 8 characters long');
          }

          const user = await context.resource.findOne(userId);
          if (!user) {
            throw new Error('User not found');
          }

          // Hash password
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          // Update user password
          await context.resource.update(userId, {
            password: hashedPassword
          });

          return {
            notice: {
              message: 'Password successfully updated',
              type: 'success'
            }
          };
        },
      }
    }
  }
};

