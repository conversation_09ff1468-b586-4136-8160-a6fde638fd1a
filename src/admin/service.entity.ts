import {BaseEntity, Column, Entity, PrimaryGeneratedColumn} from 'typeorm';
import crypto from 'crypto';
import * as moment from 'moment';
import { Product } from './models/product.entity.js';
import { OneToOne, JoinColumn } from 'typeorm';
export enum ServiceStatus {
  Pending = 'Pending',
  Active = 'Active',
  Suspended = 'Suspended',
  Terminated = 'Terminated',
  Cancelled = 'Cancelled',
  Fraud = 'Fraud',
}

@Entity('tblhosting')
export class Service extends BaseEntity  {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'int', nullable: false})
  userid: number;

  @Column({type: 'int', nullable: false})
  orderid: string;

  @Column({type: 'int', nullable: false})
  packageid: number;

  @Column({type: 'varchar', nullable: false})
  billingcycle: string;

  @Column({type: 'date', nullable: false})
  regdate: Date;

  @Column({type: 'date', nullable: false})
  nextduedate: Date;

  @Column({type: 'date', nullable: false})
  nextinvoicedate: Date;

  @Column({name: 'termination_date', type: 'date', nullable: false})
  terminationDate: Date;

  @Column({name: 'completed_date', type: 'date', nullable: false})
  completedDate: Date;

  @Column({name: 'lastupdate', type: 'date', nullable: false})
  lastUpdate: Date;

  @Column({type: 'varchar', nullable: false})
  domainstatus: string;

  @Column({type: 'decimal'})
  firstpaymentamount: number;

  @Column({type: 'decimal'})
  amount: number;

  @OneToOne(type => Product)
  @JoinColumn({ name: 'packageid' })
  product: Product;

  @Column({type: 'varchar'})
  notes: string;

  @Column()
  server: number;

  @Column({name: 'paymentmethod', type: 'text'})
  paymentMethod: string;

  @Column({type: 'varchar'})
  domain: string;

  @Column({type: 'text'})
  username: string;

  @Column({type: 'text'})
  password: string;

  @Column({name: 'subscriptionid', type: 'text'})
  subscriptionId: string;

  @Column({name: 'promoid', type: 'int'})
  promoId: number;

  @Column({name: 'promocount', type: 'int'})
  promoCount: number;

  @Column({name: 'suspendreason', type: 'text'})
  suspendReason: string;

  @Column({name: 'overideautosuspend', type: 'tinyint'})
  overideAutoSuspend: number;

  @Column({name: 'overidesuspenduntil', type: 'date'})
  overideSuspendUntil: Date;

  @Column({name: 'dedicatedip', type: 'text'})
  dedicatedIp: string;

  @Column({name: 'assignedips', type: 'text'})
  assignedIps: string;

  @Column({type: 'text'})
  ns1: string;

  @Column({type: 'text'})
  ns2: string;

  toJson() {

    return {
      id: this.id,
      userid : this.userid,
      orderid : this.orderid,
      packageid : this.packageid,
      regdate : this.regdate,
      nextduedate : this.nextduedate,
      nextinvoicedate : this.nextinvoicedate,
      domainstatus : this.domainstatus,
      firstpaymentamount: this.firstpaymentamount,
      recurringamount: this.amount,
    };
  }
  daysTillDue(): number {
    return moment.duration(moment.utc(this.nextduedate).diff(moment.utc())).asDays();
  }

  isDue(): boolean {
    return this.daysTillDue() < 0;
  }

  saveKevValue(key: string, value: any) {
    if (this.notes === '') {
      this.notes = JSON.stringify({key: value});
    } else {
      const originNotes = JSON.parse(this.notes);
      originNotes[key] = value;
      this.notes = JSON.stringify(originNotes);
    }
  }

  getKeyValue(key: string): any {
    if (this.notes === '') {
      return null;
    } else {
      const originNotes = JSON.parse(this.notes);
      return originNotes[key];
    }
  }

  static token(id): string {
    return crypto.createHash('md5').update(`${id}-jss-is-fucking-awesome`).digest('hex');
  }
}