import { Module } from '@nestjs/common';
import { AdminModule } from '@adminjs/nestjs';
import { ConfigModule } from '@nestjs/config';

import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import provider from './admin/auth-provider.js';
import options from './admin/options.js';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as AdminJSTypeorm from '@adminjs/typeorm';
import AdminJS from 'adminjs';
import { User } from './admin/models/user.entity.js';
import { Service } from './admin/models/service.entity.js';
import { Pricing } from './admin/models/pricing.entity.js';
import { Product } from './admin/models/product.entity.js';
import { Invoice } from './admin/models/invoice.entity.js';
import { InvoiceItem } from './admin/models/invoice.entity.js';
import { Wallet } from './admin/models/wallet.entity.js';
import { Event } from './admin/models/events.entity.js';
import { ConfigService } from './config.service.js';
import { NotifyEmail } from './admin/models/notify-email.entity.js';
AdminJS.registerAdapter({
  Resource: AdminJSTypeorm.Resource,
  Database: AdminJSTypeorm.Database,
})

if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'dev';
}
const config = new ConfigService(`${process.env.NODE_ENV}.env`);

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: `envs/${process.env.NODE_ENV}.env`,
    }),
    TypeOrmModule.forRoot({
      name: 'whmcs',
      type: 'mysql',  
      host: config.get('DB_HOST'),
      port: 3306,
      username: config.get('DB_USER'),
      password: config.get('DB_PASS'),
      database: config.get('WHMCS_DB_NAME'),
      entities: [User, Product, Pricing, Service, Invoice, InvoiceItem],
      synchronize: false,
      supportBigNumbers: true,
      bigNumberStrings: false,
      extra: {
        connectionLimit: 7,
      },
    }),
    TypeOrmModule.forRoot({
      name: 'api',
      type: 'mysql',
      host: config.get('DB_HOST'),
      port: 3306,
      username: config.get('DB_USER'),
      password: config.get('DB_PASS'),
      database: config.get('API_DB_NAME'),
      entities: [Wallet, Event],
      synchronize: false,
      supportBigNumbers: true,
      bigNumberStrings: false,
      extra: {
        connectionLimit: 7,
      },
    }),
    TypeOrmModule.forRoot({
      name: 'notify',
      type: 'mysql',
      host: config.get('DB_HOST'),
      port: 3306,
      username: config.get('DB_USER'),
      password: config.get('DB_PASS'),
      database: config.get('NOTIFY_DB_NAME'),
      entities: [NotifyEmail],
      synchronize: false,
      supportBigNumbers: true,
      bigNumberStrings: false,
      extra: {
        connectionLimit: 7,
      },
    }),
    AdminModule.createAdminAsync({
      useFactory: async () => {
        return {
          adminJsOptions: options,
          auth: {
            provider,
            cookiePassword: process.env.COOKIE_SECRET,
            cookieName: 'adminjs',
          },
          sessionOptions: {
            resave: true,
            saveUninitialized: true,
            secret: process.env.COOKIE_SECRET,
          },
        };
      },
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
