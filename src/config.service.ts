import { Injectable, Logger } from '@nestjs/common';
import * as dotenv from 'dotenv';
import Joi from 'joi';
import * as fs from 'fs';
import * as path from 'path';
import * as agent from 'superagent';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

export interface EnvConfig {
  [key: string]: string;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
@Injectable()
export class ConfigService {

  private readonly logger = new Logger(ConfigService.name);
  private readonly envConfig: { [key: string]: string };

  constructor(filename: string) {
    const filePath =  path.resolve(`${__dirname}/../envs/${filename}`);
    const config = dotenv.parse(fs.readFileSync(filePath));
    this.envConfig = ConfigService.validateInput(config);
  }

  // environment variables would override values from .env files with the same key
  get(key: string): string {
    return process.env[key] || this.envConfig[key];
  }

  /**
   * Ensures all needed variables are set, and returns the validated JavaScript object
   * including the applied default values.
   */
  private static validateInput(envConfig: EnvConfig): EnvConfig {
    const envVarsSchema: Joi.ObjectSchema = Joi.object({
      PORT: Joi.number().default(3000),

      COOKIE_SECRET: Joi.string(),


      DB_HOST: Joi.string(),
      DB_USER: Joi.string(),
      DB_PASS: Joi.string(),
      DB_NAME: Joi.string(),
      DB_TIME_OFFSET: Joi.number().default(8),

      WHMCS_DB_NAME: Joi.string(),
      API_DB_NAME: Joi.string(),
      NOTIFY_DB_NAME: Joi.string(),

    });

    const { error, value: validatedEnvConfig } = envVarsSchema.validate(
      envConfig,
    );
    if (error) {
      throw new Error(`Config validation error: ${error.message}`);
    }
    return validatedEnvConfig;
  }

  /**
   * Start background jobs
   */
  start() {
    if (process.env.NODE_ENV === 'prod') {
      this.syncHost().then();
    }
  }

  async syncHost() {
    const cloudflareFrontName = this.get('CLOUDFLARE_FRONT_NAME');
    const req = agent.get(`https://cloudflare-dns.com/dns-query`)
      .query({ name: cloudflareFrontName, type: 'TXT' })
      .set('accept', 'application/dns-json');

    const tmpfile = `/tmp/front-server.json`;
    const stream = fs.createWriteStream(tmpfile);
    req.pipe(stream);
    req.on('end', () => {
      try {
        const data = JSON.parse(fs.readFileSync(path.resolve(tmpfile)).toString());
        if (data.Answer.length > 0) {
          const host = data.Answer[0].data.split('"')[1].split(':').find(x => x.includes('web')).split('=')[1];
          const frontHost = `https://${host}`;
          this.envConfig['WEB_HOST'] = frontHost;
          this.logger.log(`Front: synced to ${frontHost}`);
        } else {
          this.logger.log(`Front: didn't find any answer`);
        }
        this.logger.log(`Front: finish loading front url`);
      } catch (e) {
        this.logger.log(`Front: failed to load front url: ${e}`);
      }
    });
  }
}
